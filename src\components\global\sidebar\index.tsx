"use client"
import { getWorkSpaces } from '@/actions/workspace'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import userQueryData from '@/hooks/userQueryData'
import { NotificationProps, WorkspaceProps } from '@/types/index.type'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'
import React from 'react'
import Modal from '../modal'
import { PlusCircle } from 'lucide-react'
import Search from '../search'
import { MENU_ITEMS } from '@/constants'
import SidebarItem from './sidebar-items'
import { getNotifications } from '@/actions/user'
import WorkspacePlaceholder from './workspace-placeholder'

type Props = {
    activeWorkspaceId: string
}

const Sidebar = ({ activeWorkspaceId }: Props) => {
    const router = useRouter()
    const pathName = usePathname()

    const { data, isFetched } = userQueryData(
        ['user-workspaces'],
        getWorkSpaces
    )
    const menuItems = MENU_ITEMS(activeWorkspaceId);

    const { data: notifications } = userQueryData(
        ['user-notifications'],
        getNotifications
    )

    const { data: workspaces } = data as WorkspaceProps
    const { data: count } = notifications as NotificationProps

    const onChangeActiveWorkspace = (value: string) => {
        router.push(`/dashboard/${value}`)
    }

    const currentWorkspace = workspaces.workspace.find(
        (s) => s.id === activeWorkspaceId
    )



    return (
        <div
            className='bg-[#111111] flex-none relative p-4 h-full w-[250px] flex 
            flex-col gap-4 items-center overflow-hidden'
        >
            <div className='bg-[#111111] p-4 flex gap-2 justify-center items-center mb-4
            absolute top-0 left-0 right-0'>
                <Image
                    src='/saul-logo.png'
                    height={50}
                    width={50}
                    alt='logo'

                />
                <p className='text-2xl'>Noah</p>
            </div>

            <Select
                defaultValue={activeWorkspaceId}
                onValueChange={onChangeActiveWorkspace}
            >
                <SelectTrigger className='w-full mt-18 text-neutral-400 bg-transparent'>
                    <SelectValue placeholder="Select a workspace"></SelectValue>
                </SelectTrigger>
                <SelectContent className='bg-[#111111] backdrop-blur-xl'>
                    <SelectGroup>
                        <SelectLabel>Workspaces</SelectLabel>
                        <Separator />
                        {workspaces.workspace.map((workspace) => (
                            <SelectItem
                                key={workspace.id}
                                value={workspace.id}
                            >
                                {workspace.name}
                            </SelectItem>
                        ))}
                        {workspaces.members.length > 0 &&
                            workspaces.members.map(
                                (workspace) => workspace.Workspace && (
                                    <SelectItem
                                        value={workspace.Workspace.id}
                                        key={workspace.Workspace.id}
                                    >
                                        {workspace.Workspace.name}
                                    </SelectItem>
                                )
                            )}
                    </SelectGroup>
                </SelectContent>
            </Select>
            {currentWorkspace?.type === 'PUBLIC' &&
                workspaces.subscription?.plan === 'PRO' && (
                    <Modal
                        trigger={
                            <span className='text-sm cursor-pointer flex items-center
                    justify-center border-t-neutral-800/90 hover:bg-neutral-800/60
                    w-full rounded-s-sm p-[5px] gap-2'>
                                <PlusCircle
                                    size={15}
                                    className='text-neutral-800/90 fill-neutral-500'
                                />

                                <span className='text-neutral-400 font-semibold text-xs'>
                                    Invite To Workspace
                                </span>
                            </span>
                        }
                        title="Invite To Workspace"
                        description="Invite other users to your workspace"
                    >
                        <Search workspaceId={activeWorkspaceId} />
                    </Modal>
                )}
                
                <p className='w-full text-[#9D9D9D] font-bold mt-4'>
                    Menu
                </p>
                <nav className='w-full'>
                    <ul>{menuItems.map((item) => (
                        <SidebarItem
                            href={item.href}
                            icon={item.icon}
                            selected={pathName === item.href}
                            title={item.title}
                            key={item.title}
                            notifications={
                                (item.title === 'Notifications' && 
                                    count._count &&
                                    count._count.notification ) || 
                                0
                            }
                        />
                    ))}</ul>
                </nav>
                <Separator className='w-4/5' />
                <p className='w-full text-[#9D9D9D] font-bold mt-4'>Workspaces</p>
                <nav className='w-full'>
                    <ul className='h-[150px] overflow-auto overflow-x-hidden fade-layer'>
                        {workspaces.workspace.length > 0 && 
                            workspaces.workspace.map((item) => (
                                <SidebarItem 
                                    href={`/dashboard/${item.id}`}
                                    selected={pathName === `/dashboard/${item.id}`}
                                    title={item.name}
                                    notifications={0}
                                    key={item.id}
                                    icon={
                                        <WorkspacePlaceholder>
                                            {item.name.charAt(0)}
                                        </WorkspacePlaceholder>
                                    }
                                />
                        ))}
                    </ul>
                </nav>
        </div>
    )
}

export default Sidebar